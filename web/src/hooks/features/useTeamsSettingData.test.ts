import '@testing-library/jest-dom';
import { renderHook, act } from '@testing-library/react-hooks';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import useTeamsSettingData, { UseTeamsSettingDataProps, ISelectedItem } from './useTeamsSettingData';
import { IUserChatItem } from '../accessors/useUserChatsAndChannelsAccessor';
import { DbProvider } from '../../types/IGeraniumAttaneDB';
import useTeamsChatsRepositoryAccessor from '../accessors/useTeamsChatsRepositoryAccessor';
import useRemoteTeamsChatsFeature from './useRemoteTeamsChatsFeature';

// environment をモック
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_API_URL: 'https://example.com/api',
  },
}));

// useTeamsChatsRepositoryAccessor をモック
jest.mock('../accessors/useTeamsChatsRepositoryAccessor', () => ({
  __esModule: true,
  default: jest.fn(),
}));

// useRemoteTeamsChatsFeature をモック
jest.mock('./useRemoteTeamsChatsFeature', () => ({
  __esModule: true,
  default: jest.fn(),
}));

const mockUseTeamsChatsRepositoryAccessor = useTeamsChatsRepositoryAccessor as jest.Mock;
const mockUseRemoteTeamsChatsFeature = useRemoteTeamsChatsFeature as jest.Mock;

describe('useTeamsSettingData', () => {
  // モックデータの作成
  const mockEventReporter: EventReporter = jest.fn();
  const mockOpenDB: DbProvider = jest.fn();

  const mockUserChatItems: IUserChatItem[] = [
    {
      id: 'chat-1',
      name: 'Test Chat 1',
      type: 'チャット',
      chatType: 'oneOnOne',
    },
    {
      id: 'channel-1',
      name: 'Test Team - General',
      type: 'チャネル',
      chatType: 'TeamsChannel',
      teamId: 'team-1',
    },
  ];

  const mockTeamsChatsResponse = [
    {
      chatId: 'chat-1',
      channelId: null,
      name: 'Test Chat 1',
      type: 'チャット',
      countId: 1,
      chatType: 'oneOnOne',
      teamId: undefined,
    },
  ];

  const mockProps: UseTeamsSettingDataProps = {
    fetchUserChatsTotalCount: jest.fn(),
    fetchUserTeamsTotalCount: jest.fn(),
    getTeamsChatsApi: jest.fn(),
    postTeamsChatsApi: jest.fn(),
    deleteTeamsChatsApi: jest.fn(),
    isModalOpen: false,
    openDB: mockOpenDB,
    eventReporter: mockEventReporter,
  };

  const mockRepositoryReturn = {
    isInitialized: true,
    isTransactionPending: false,
    allTeamsChats: [],
    retrieveTeamsChats: jest.fn(),
    addTeamsChats: jest.fn(),
    deleteTeamsChats: jest.fn(),
    replaceTeamsChats: jest.fn(),
    getTeamsChatsQueues: jest.fn(),
    addTeamsChatsQueue: jest.fn(),
    deleteTeamsChatsQueue: jest.fn(),
    replaceTeamsChatsQueues: jest.fn(),
  };

  const mockRemoteFeature = {
    addRemoteTeamsChats: jest.fn(),
    deleteRemoteTeamsChats: jest.fn(),
    fetchRemoteTeamsChats: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseTeamsChatsRepositoryAccessor.mockReturnValue(mockRepositoryReturn);
    mockUseRemoteTeamsChatsFeature.mockReturnValue(mockRemoteFeature);
  });

  describe('useTeamsSettingData hook', () => {
    it('初期状態で正しい値を返すこと', () => {
      const { result } = renderHook(() => useTeamsSettingData(mockProps));

      expect(result.current.allChatItems).toEqual([]);
      expect(result.current.savedItems).toEqual(new Set());
      expect(result.current.savedSelectedItems).toEqual(new Set());
      expect(result.current.isLoadingSavedItems).toBe(false);
      expect(result.current.hasMoreChats).toBe(false);
      expect(result.current.nextPageToken).toBeUndefined();
      expect(result.current.isLoadingMore).toBe(false);
      expect(result.current.loadSavedItems).toBeInstanceOf(Function);
      expect(result.current.saveSelectedItems).toBeInstanceOf(Function);
      expect(result.current.loadMoreChats).toBeInstanceOf(Function);
      expect(result.current.setAllChatItems).toBeInstanceOf(Function);
      expect(result.current.setSavedItems).toBeInstanceOf(Function);
    });

    it('モーダルが開いた時にデータ取得処理が実行されること', async () => {
      const mockFetchUserChatsAndChannels = jest.fn().mockResolvedValue(mockUserChatItems);
      const mockGetTeamsChatsApi = jest.fn().mockResolvedValue(mockTeamsChatsResponse);

      const propsWithModal = {
        ...mockProps,
        isModalOpen: true,
        fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannels,
        getTeamsChatsApi: mockGetTeamsChatsApi,
      };

      const { result, waitForNextUpdate } = renderHook(() => useTeamsSettingData(propsWithModal));

      await waitForNextUpdate();

      expect(mockFetchUserChatsAndChannels).toHaveBeenCalled();
      expect(result.current.allChatItems).toEqual(mockUserChatItems);
    });
  });

  describe('loadSavedItems', () => {
    it('getTeamsChatsApiが存在しない場合、何もしないこと', async () => {
      const propsWithoutApi = {
        ...mockProps,
        getTeamsChatsApi: undefined,
      };

      const { result } = renderHook(() => useTeamsSettingData(propsWithoutApi));

      await act(async () => {
        await result.current.loadSavedItems();
      });

      expect(result.current.isLoadingSavedItems).toBe(false);
    });

    it('正常に保存済みアイテムを取得すること', async () => {
      const mockGetTeamsChatsApi = jest.fn().mockResolvedValue(mockTeamsChatsResponse);
      const propsWithApi = {
        ...mockProps,
        getTeamsChatsApi: mockGetTeamsChatsApi,
      };

      const { result } = renderHook(() => useTeamsSettingData(propsWithApi));

      await act(async () => {
        await result.current.loadSavedItems();
      });

      expect(mockGetTeamsChatsApi).toHaveBeenCalled();
      expect(result.current.savedItems).toEqual(new Set(['chat-1']));

      // savedSelectedItemsも正しく設定されることを確認
      const expectedSelectedItem: ISelectedItem = {
        id: 'chat-1',
        name: 'Test Chat 1',
        type: 'チャット',
        chatType: 'oneOnOne',
        teamId: undefined,
        countId: 1,
      };
      expect(Array.from(result.current.savedSelectedItems)).toEqual([expectedSelectedItem]);
      expect(result.current.isLoadingSavedItems).toBe(false);
    });

    it('API呼び出しでエラーが発生した場合、エラーをスローすること', async () => {
      const mockError = new Error('API Error');
      const mockGetTeamsChatsApi = jest.fn().mockRejectedValue(mockError);
      const propsWithApi = {
        ...mockProps,
        getTeamsChatsApi: mockGetTeamsChatsApi,
      };

      const { result } = renderHook(() => useTeamsSettingData(propsWithApi));

      await act(async () => {
        try {
          await result.current.loadSavedItems();
        } catch (error) {
          expect(error).toBeInstanceOf(Error);
          expect((error as Error).message).toContain('保存済みアイテムの取得に失敗しました');
        }
      });

      expect(result.current.isLoadingSavedItems).toBe(false);
    });
  });

  describe('saveSelectedItems', () => {
    it('リモート機能が利用できない場合、エラーをスローすること', async () => {
      const mockRemoteFeatureWithoutFunctions = {
        addRemoteTeamsChats: undefined,
        deleteRemoteTeamsChats: undefined,
        fetchRemoteTeamsChats: jest.fn(),
      };
      mockUseRemoteTeamsChatsFeature.mockReturnValue(mockRemoteFeatureWithoutFunctions);

      const { result } = renderHook(() => useTeamsSettingData(mockProps));

      const selectedItems: Set<ISelectedItem> = new Set([
        {
          id: 'chat-1',
          name: 'Test Chat 1',
          type: 'チャット',
          chatType: 'oneOnOne',
          countId: 0,
        },
      ]);

      await act(async () => {
        try {
          await result.current.saveSelectedItems(selectedItems);
        } catch (error) {
          expect(error).toBeInstanceOf(Error);
          expect((error as Error).message).toBe('リモート機能が利用できません');
        }
      });
    });

    it('選択されたアイテムを正常に保存すること', async () => {
      const mockAddRemoteTeamsChats = jest.fn().mockResolvedValue(undefined);
      const mockDeleteRemoteTeamsChats = jest.fn().mockResolvedValue(undefined);

      const mockRemoteFeatureWithFunctions = {
        addRemoteTeamsChats: mockAddRemoteTeamsChats,
        deleteRemoteTeamsChats: mockDeleteRemoteTeamsChats,
        fetchRemoteTeamsChats: jest.fn(),
      };
      mockUseRemoteTeamsChatsFeature.mockReturnValue(mockRemoteFeatureWithFunctions);

      // 保存済みアイテムを持つpropsでhookを初期化
      const mockGetTeamsChatsApi = jest.fn().mockResolvedValue([
        {
          chatId: 'chat-1',
          channelId: null,
          name: 'Test Chat 1',
          type: 'チャット',
          countId: 1,
          chatType: 'oneOnOne',
          teamId: undefined,
        },
      ]);

      const propsWithSavedItems = { ...mockProps, getTeamsChatsApi: mockGetTeamsChatsApi };
      const { result } = renderHook(() => useTeamsSettingData(propsWithSavedItems));

      // 保存済みアイテムを読み込み
      await act(async () => {
        await result.current.loadSavedItems();
      });

      const selectedItems: Set<ISelectedItem> = new Set([
        {
          id: 'channel-1',
          name: 'Test Team - General',
          type: 'チャネル',
          chatType: 'TeamsChannel',
          teamId: 'team-1',
          countId: 0,
        },
      ]);

      await act(async () => {
        await result.current.saveSelectedItems(selectedItems);
      });

      // 削除対象（chat-1）の削除処理が呼ばれること
      expect(mockDeleteRemoteTeamsChats).toHaveBeenCalledWith({
        id: 'chat-1',
        name: 'Test Chat 1',
        type: 'チャット',
        chatType: 'oneOnOne',
        countId: 0,
      });

      // 新規追加対象（channel-1）の追加処理が呼ばれること
      expect(mockAddRemoteTeamsChats).toHaveBeenCalledWith({
        id: 'channel-1',
        name: 'Test Team - General',
        type: 'チャネル',
        chatType: 'TeamsChannel',
        teamId: 'team-1',
        countId: 1,
      });

      // 保存済みアイテムが更新されること
      expect(result.current.savedItems).toEqual(new Set(['channel-1']));
      expect(result.current.savedSelectedItems).toEqual(selectedItems);
    });

    it('削除対象アイテムが見つからない場合、エラーをスローすること', async () => {
      const mockRemoteFeatureWithFunctions = {
        addRemoteTeamsChats: jest.fn(),
        deleteRemoteTeamsChats: jest.fn(),
        fetchRemoteTeamsChats: jest.fn(),
      };
      mockUseRemoteTeamsChatsFeature.mockReturnValue(mockRemoteFeatureWithFunctions);

      const { result } = renderHook(() => useTeamsSettingData(mockProps));

      // 存在しないアイテムを保存済みとして設定
      act(() => {
        result.current.setSavedItems(new Set(['non-existent-id']));
        // savedSelectedItemsは空のまま（削除対象が見つからない状況を作る）
      });

      const emptySelectedItems: Set<ISelectedItem> = new Set();

      await act(async () => {
        try {
          await result.current.saveSelectedItems(emptySelectedItems);
        } catch (error) {
          expect(error).toBeInstanceOf(Error);
          expect((error as Error).message).toContain('削除対象アイテムが見つかりません');
        }
      });
    });
  });

  describe('ページネーション機能', () => {
    // ページネーション対応のデータ取得をテスト
    it('fetchUserChatsAndChannelsPaginatedが提供された場合、初回20件を取得する', async () => {
      const mockPaginatedResult = {
        items: mockUserChatItems.slice(0, 2),
        hasMore: true,
        nextPageToken: 'next-page-token',
      };

      const mockFetchUserChatsAndChannelsPaginated = jest
        .fn().mockResolvedValue(mockPaginatedResult);

      const props: UseTeamsSettingDataProps = {
        fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannelsPaginated,
        getTeamsChatsApi: jest.fn().mockResolvedValue([]),
        isModalOpen: true,
        openDB: mockOpenDB,
        eventReporter: mockEventReporter,
      };

      const { result } = renderHook(() => useTeamsSettingData(props));

      // 初期状態の確認
      expect(result.current.allChatItems).toEqual([]);
      expect(result.current.hasMoreChats).toBe(false);
      expect(result.current.nextPageToken).toBeUndefined();

      // データが取得されるまで待機
      await act(async () => {
        await Promise.resolve();
      });

      // ページネーション機能が呼ばれることを確認
      expect(mockFetchUserChatsAndChannelsPaginated).toHaveBeenCalledWith();

      // 状態が更新されることを確認
      expect(result.current.allChatItems).toEqual(mockUserChatItems.slice(0, 2));
      expect(result.current.hasMoreChats).toBe(true);
      expect(result.current.nextPageToken).toBe('next-page-token');
    });

    // さらに読み込む機能をテスト
    it('loadMoreChatsで追加データを取得する', async () => {
      const initialResult = {
        items: mockUserChatItems.slice(0, 1),
        hasMore: true,
        nextPageToken: 'next-page-token',
      };

      const additionalResult = {
        items: mockUserChatItems.slice(1, 2),
        hasMore: false,
        nextPageToken: undefined,
      };

      const mockFetchUserChatsAndChannelsPaginated = jest.fn()
        .mockResolvedValueOnce(initialResult)
        .mockResolvedValueOnce(additionalResult);

      const props: UseTeamsSettingDataProps = {
        fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannelsPaginated,
        getTeamsChatsApi: jest.fn().mockResolvedValue([]),
        isModalOpen: true,
        openDB: mockOpenDB,
        eventReporter: mockEventReporter,
      };

      const { result } = renderHook(() => useTeamsSettingData(props));

      // 初回データ取得を待機
      await act(async () => {
        await Promise.resolve();
      });

      // 初回データが取得されることを確認
      expect(result.current.allChatItems).toHaveLength(1);
      expect(result.current.hasMoreChats).toBe(true);

      // さらに読み込むを実行
      await act(async () => {
        await result.current.loadMoreChats();
      });

      // 追加データが取得されることを確認
      expect(mockFetchUserChatsAndChannelsPaginated).toHaveBeenCalledWith('next-page-token');
      expect(result.current.allChatItems).toHaveLength(2);
      expect(result.current.hasMoreChats).toBe(false);
      expect(result.current.nextPageToken).toBeUndefined();
    });

    // loadMoreChatsの条件テスト
    it('fetchUserChatsAndChannelsPaginatedが存在しない場合、loadMoreChatsは何もしない', async () => {
      const props: UseTeamsSettingDataProps = {
        getTeamsChatsApi: jest.fn().mockResolvedValue([]),
        isModalOpen: true,
        openDB: mockOpenDB,
        eventReporter: mockEventReporter,
      };

      const { result } = renderHook(() => useTeamsSettingData(props));

      await act(async () => {
        await result.current.loadMoreChats();
      });

      // 何も変更されないことを確認
      expect(result.current.allChatItems).toEqual([]);
      expect(result.current.hasMoreChats).toBe(false);
    });

    it('nextPageTokenが存在しない場合、loadMoreChatsは何もしない', async () => {
      const mockFetchUserChatsAndChannelsPaginated = jest.fn();

      const props: UseTeamsSettingDataProps = {
        fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannelsPaginated,
        getTeamsChatsApi: jest.fn().mockResolvedValue([]),
        isModalOpen: false,
        openDB: mockOpenDB,
        eventReporter: mockEventReporter,
      };

      const { result } = renderHook(() => useTeamsSettingData(props));

      await act(async () => {
        await result.current.loadMoreChats();
      });

      // fetchUserChatsAndChannelsPaginatedが呼ばれないことを確認
      expect(mockFetchUserChatsAndChannelsPaginated).not.toHaveBeenCalled();
    });

    it('isLoadingMoreがtrueの場合、loadMoreChatsは何もしない', async () => {
      const initialResult = {
        items: mockUserChatItems.slice(0, 1),
        hasMore: true,
        nextPageToken: 'next-page-token',
      };

      const mockFetchUserChatsAndChannelsPaginated = jest.fn()
        .mockResolvedValue(initialResult);

      const props: UseTeamsSettingDataProps = {
        fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannelsPaginated,
        getTeamsChatsApi: jest.fn().mockResolvedValue([]),
        isModalOpen: true,
        openDB: mockOpenDB,
        eventReporter: mockEventReporter,
      };

      const { result } = renderHook(() => useTeamsSettingData(props));

      // 初回データ取得を待機
      await act(async () => {
        await Promise.resolve();
      });

      // isLoadingMoreをtrueにするために、loadMoreChatsを並行実行
      const loadMorePromise1 = act(async () => {
        await result.current.loadMoreChats();
      });

      const loadMorePromise2 = act(async () => {
        await result.current.loadMoreChats();
      });

      await Promise.all([loadMorePromise1, loadMorePromise2]);

      // fetchUserChatsAndChannelsPaginatedが2回以上呼ばれないことを確認（初回 + 1回のみ）
      expect(mockFetchUserChatsAndChannelsPaginated).toHaveBeenCalledTimes(2);
    });

    // 後方互換性のテスト
    it('fetchUserChatsAndChannelsPaginatedが提供された場合、ページネーション取得を行う', async () => {
      const mockFetchUserChatsAndChannelsPaginated = jest.fn().mockResolvedValue({
        items: mockUserChatItems,
        hasMore: false,
        nextPageToken: undefined,
      });

      const props: UseTeamsSettingDataProps = {
        fetchUserChatsAndChannelsPaginated: mockFetchUserChatsAndChannelsPaginated,
        fetchUserChatsTotalCount: jest.fn(),
        fetchUserTeamsTotalCount: jest.fn(),
        getTeamsChatsApi: jest.fn().mockResolvedValue([]),
        isModalOpen: true,
        openDB: mockOpenDB,
        eventReporter: mockEventReporter,
      };

      const { result } = renderHook(() => useTeamsSettingData(props));

      // データが取得されるまで待機
      await act(async () => {
        await Promise.resolve();
      });

      // ページネーション関数が呼ばれることを確認
      expect(mockFetchUserChatsAndChannelsPaginated).toHaveBeenCalled();

      // 全件取得の場合はページネーション状態がfalseになることを確認
      expect(result.current.allChatItems).toEqual(mockUserChatItems);
      expect(result.current.hasMoreChats).toBe(false);
      expect(result.current.nextPageToken).toBeUndefined();
    });
  });
});
